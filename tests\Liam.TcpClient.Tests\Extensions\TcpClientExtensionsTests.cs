using FluentAssertions;
using Liam.TcpClient.Extensions;
using Liam.TcpClient.Interfaces;
using Liam.TcpClient.Models;
using Moq;
using System.Text.Json;
using Xunit;

namespace Liam.TcpClient.Tests.Extensions;

/// <summary>
/// TCP客户端扩展方法测试
/// </summary>
public class TcpClientExtensionsTests
{
    private readonly Mock<ITcpClient> _mockClient;

    public TcpClientExtensionsTests()
    {
        _mockClient = new Mock<ITcpClient>();
    }

    [Fact]
    public async Task SendJsonAsync_ShouldSerializeAndSendObject()
    {
        // Arrange
        var testObject = new { Name = "Test", Value = 123 };
        var expectedJson = JsonSerializer.Serialize(testObject);
        
        _mockClient.Setup(c => c.SendTextAsync(expectedJson, It.IsAny<CancellationToken>()))
                   .ReturnsAsync(true);

        // Act
        var result = await _mockClient.Object.SendJsonAsync(testObject);

        // Assert
        result.Should().BeTrue();
        _mockClient.Verify(c => c.SendTextAsync(expectedJson, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task SendJsonAsync_ShouldThrowArgumentNullException_WhenClientIsNull()
    {
        // Arrange
        ITcpClient? nullClient = null;
        var testObject = new { Name = "Test" };

        // Act & Assert
        var act = async () => await nullClient!.SendJsonAsync(testObject);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("client");
    }

    [Fact]
    public async Task SendJsonAsync_ShouldThrowArgumentNullException_WhenObjectIsNull()
    {
        // Act & Assert
        var act = async () => await _mockClient.Object.SendJsonAsync<object>(null!);
        await act.Should().ThrowAsync<ArgumentNullException>().WithParameterName("obj");
    }

    [Fact]
    public async Task ReceiveJsonAsync_ShouldDeserializeReceivedText()
    {
        // Arrange
        var testObject = new TestModel { Name = "Test", Value = 123 };
        var json = JsonSerializer.Serialize(testObject);
        
        _mockClient.Setup(c => c.ReceiveTextAsync(It.IsAny<TimeSpan?>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(json);

        // Act
        var result = await _mockClient.Object.ReceiveJsonAsync<TestModel>();

        // Assert
        result.Should().NotBeNull();
        result!.Name.Should().Be(testObject.Name);
        result.Value.Should().Be(testObject.Value);
    }

    [Fact]
    public async Task SendBatchAsync_ShouldSendAllMessages_WhenAllSucceed()
    {
        // Arrange
        var messages = new[]
        {
            TcpMessage.CreateTextMessage("Message 1"),
            TcpMessage.CreateTextMessage("Message 2"),
            TcpMessage.CreateTextMessage("Message 3")
        };

        _mockClient.Setup(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(true);

        // Act
        var result = await _mockClient.Object.SendBatchAsync(messages);

        // Assert
        result.Should().Be(3);
        _mockClient.Verify(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()), Times.Exactly(3));
    }

    [Fact]
    public async Task SendBatchAsync_ShouldReturnPartialCount_WhenSomeFail()
    {
        // Arrange
        var messages = new[]
        {
            TcpMessage.CreateTextMessage("Message 1"),
            TcpMessage.CreateTextMessage("Message 2"),
            TcpMessage.CreateTextMessage("Message 3")
        };

        _mockClient.SetupSequence(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(true)   // 第一个成功
                   .ReturnsAsync(false)  // 第二个失败
                   .ReturnsAsync(true);  // 第三个成功

        // Act
        var result = await _mockClient.Object.SendBatchAsync(messages);

        // Assert
        result.Should().Be(2); // 只有2个成功
    }

    [Fact]
    public async Task SendBatchAsync_WithConcurrency_ShouldRespectMaxConcurrency()
    {
        // Arrange
        var messages = Enumerable.Range(1, 10)
            .Select(i => TcpMessage.CreateTextMessage($"Message {i}"))
            .ToArray();

        var concurrentCalls = 0;
        var maxConcurrentCalls = 0;
        var lockObject = new object();

        _mockClient.Setup(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                   .Returns(async (TcpMessage msg, CancellationToken ct) =>
                   {
                       lock (lockObject)
                       {
                           concurrentCalls++;
                           maxConcurrentCalls = Math.Max(maxConcurrentCalls, concurrentCalls);
                       }

                       await Task.Delay(50, ct); // 模拟网络延迟

                       lock (lockObject)
                       {
                           concurrentCalls--;
                       }

                       return true;
                   });

        // Act
        var result = await _mockClient.Object.SendBatchAsync(messages, maxConcurrency: 3);

        // Assert
        result.Should().Be(10);
        maxConcurrentCalls.Should().BeLessOrEqualTo(3);
    }

    [Fact]
    public async Task SendBatchTextAsync_ShouldSendAllTexts()
    {
        // Arrange
        var texts = new[] { "Text 1", "Text 2", "Text 3" };

        _mockClient.Setup(c => c.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(true);

        // Act
        var result = await _mockClient.Object.SendBatchTextAsync(texts);

        // Assert
        result.Should().Be(3);
        _mockClient.Verify(c => c.SendTextAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Exactly(3));
    }

    [Fact]
    public async Task SendBatchAsync_ShouldReturnZero_WhenEmptyCollection()
    {
        // Arrange
        var emptyMessages = Array.Empty<TcpMessage>();

        // Act
        var result = await _mockClient.Object.SendBatchAsync(emptyMessages);

        // Assert
        result.Should().Be(0);
        _mockClient.Verify(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task SendBatchAsync_ShouldUseSequentialSending_ForSmallBatches()
    {
        // Arrange
        var messages = new[]
        {
            TcpMessage.CreateTextMessage("Message 1"),
            TcpMessage.CreateTextMessage("Message 2")
        };

        _mockClient.Setup(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(true);

        // Act
        var result = await _mockClient.Object.SendBatchAsync(messages, maxConcurrency: 10);

        // Assert
        result.Should().Be(2);
        _mockClient.Verify(c => c.SendMessageAsync(It.IsAny<TcpMessage>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public void SendBatchAsync_ShouldThrowArgumentException_WhenMaxConcurrencyIsZero()
    {
        // Arrange
        var messages = new[] { TcpMessage.CreateTextMessage("Test") };

        // Act & Assert
        var act = async () => await _mockClient.Object.SendBatchAsync(messages, maxConcurrency: 0);
        act.Should().ThrowAsync<ArgumentException>().WithParameterName("maxConcurrency");
    }

    [Fact]
    public void SendBatchAsync_ShouldThrowArgumentException_WhenMaxConcurrencyIsNegative()
    {
        // Arrange
        var messages = new[] { TcpMessage.CreateTextMessage("Test") };

        // Act & Assert
        var act = async () => await _mockClient.Object.SendBatchAsync(messages, maxConcurrency: -1);
        act.Should().ThrowAsync<ArgumentException>().WithParameterName("maxConcurrency");
    }

    private class TestModel
    {
        public string Name { get; set; } = string.Empty;
        public int Value { get; set; }
    }
}
