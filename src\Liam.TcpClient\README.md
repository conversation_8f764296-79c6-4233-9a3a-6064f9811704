# Liam.TcpClient

现代化TCP客户端通信库，支持连接管理、异步数据传输、SSL/TLS安全通信、自动重连、心跳检测、连接池管理等功能，基于.NET 8.0构建。

## 功能特性

### 🔗 核心功能
- **连接管理**: 自动连接、断开、重连机制
- **异步通信**: 完全异步的数据发送和接收
- **消息协议**: 与Liam.TcpServer兼容的消息格式
- **状态监控**: 实时连接状态和事件通知
- **错误处理**: 完善的异常处理和错误恢复

### 🔒 安全特性
- **SSL/TLS支持**: 完整的SSL/TLS加密通信
- **证书验证**: 自定义证书验证回调
- **安全配置**: 灵活的安全参数配置

### 💓 高级特性
- **心跳检测**: 自动心跳保活和超时检测
- **自动重连**: 智能重连策略和重试机制
- **连接池**: 高效的连接池管理
- **性能监控**: 详细的性能指标和统计信息
- **质量评估**: 连接质量评分和健康检查

### 🎯 易用性
- **依赖注入**: 完整的DI容器支持
- **配置驱动**: 灵活的配置选项
- **事件驱动**: 丰富的事件通知机制
- **扩展方法**: 便捷的扩展功能

## 安装

### NuGet包管理器
```bash
Install-Package Liam.TcpClient
```

### .NET CLI
```bash
dotnet add package Liam.TcpClient
```

### PackageReference
```xml
<PackageReference Include="Liam.TcpClient" Version="1.0.0" />
```

## 快速开始

### 基本使用

```csharp
using Liam.TcpClient.Models;
using Liam.TcpClient.Services;
using Microsoft.Extensions.Logging;

// 创建配置
var config = new TcpClientConfig
{
    Host = "localhost",
    Port = 8080,
    EnableHeartbeat = true,
    EnableAutoReconnect = true
};

// 创建客户端
using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
var logger = loggerFactory.CreateLogger<TcpClient>();
var connectionManagerLogger = loggerFactory.CreateLogger<ConnectionManager>();
var messageHandlerLogger = loggerFactory.CreateLogger<MessageHandler>();
var heartbeatManagerLogger = loggerFactory.CreateLogger<HeartbeatManager>();

var connectionManager = new ConnectionManager(connectionManagerLogger);
var messageHandler = new MessageHandler(messageHandlerLogger);
var heartbeatManager = new HeartbeatManager(heartbeatManagerLogger, messageHandler);

using var client = new TcpClient(config, logger, connectionManager, messageHandler, heartbeatManager);

// 连接到服务器
if (await client.ConnectAsync())
{
    Console.WriteLine("连接成功！");
    
    // 发送文本消息
    await client.SendTextAsync("Hello, Server!");
    
    // 接收响应
    var response = await client.ReceiveTextAsync(TimeSpan.FromSeconds(5));
    Console.WriteLine($"收到响应: {response}");
}
```

### 依赖注入使用

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Liam.TcpClient.Extensions;

var builder = Host.CreateApplicationBuilder(args);

// 注册TCP客户端服务
builder.Services.AddTcpClient(config =>
{
    config.Host = "localhost";
    config.Port = 8080;
    config.EnableHeartbeat = true;
    config.EnableAutoReconnect = true;
});

var host = builder.Build();

// 使用TCP客户端
var client = host.Services.GetRequiredService<ITcpClient>();
await client.ConnectAsync();
```

### SSL/TLS安全连接

```csharp
var config = new TcpClientConfig
{
    Host = "secure-server.com",
    Port = 443,
    EnableSsl = true,
    SslConfig = new SslConfig
    {
        ServerName = "secure-server.com",
        CheckCertificateRevocation = true
    }
};

using var client = CreateTcpClient(config);
await client.ConnectAsync();
```

## 配置选项

### 基本配置

```csharp
var config = new TcpClientConfig
{
    Host = "localhost",                    // 服务器地址
    Port = 8080,                          // 服务器端口
    ConnectionTimeoutSeconds = 30,         // 连接超时时间
    ReceiveBufferSize = 8192,             // 接收缓冲区大小
    SendBufferSize = 8192,                // 发送缓冲区大小
    MaxMessageLength = 1024 * 1024,       // 最大消息长度
    ClientId = "client-001",              // 客户端标识
    ClientName = "MyTcpClient"            // 客户端名称
};
```

### 心跳配置

```csharp
config.EnableHeartbeat = true;
config.HeartbeatIntervalSeconds = 60;     // 心跳间隔
config.HeartbeatTimeoutSeconds = 10;      // 心跳超时时间
```

### 重连配置

```csharp
config.EnableAutoReconnect = true;
config.ReconnectIntervalSeconds = 5;      // 重连间隔
config.MaxReconnectAttempts = 10;         // 最大重连次数（-1表示无限重连）
```

### SSL配置

```csharp
config.EnableSsl = true;
config.SslConfig = new SslConfig
{
    ServerName = "example.com",
    CheckCertificateRevocation = true,
    HandshakeTimeoutSeconds = 30,
    RemoteCertificateValidationCallback = (sender, certificate, chain, errors) =>
    {
        // 自定义证书验证逻辑
        return true;
    }
};
```

## 事件处理

### 连接事件

```csharp
client.Connected += (sender, e) =>
{
    Console.WriteLine($"连接建立: {e.ConnectionInfo.RemoteEndPoint}");
    Console.WriteLine($"是否重连: {e.IsReconnection}");
};

client.Disconnected += (sender, e) =>
{
    Console.WriteLine($"连接断开: {e.Reason}");
    Console.WriteLine($"是否意外断开: {e.IsUnexpected}");
};

client.ConnectionStateChanged += (sender, e) =>
{
    Console.WriteLine($"状态变更: {e.OldState} -> {e.NewState}");
};
```

### 数据事件

```csharp
client.DataReceived += (sender, e) =>
{
    Console.WriteLine($"接收数据: {e.Length} 字节");
};

client.MessageReceived += (sender, e) =>
{
    Console.WriteLine($"接收消息: {e.Message}");
};

client.DataSent += (sender, e) =>
{
    Console.WriteLine($"发送数据: {e.Length} 字节, 成功: {e.Success}");
};
```

### 心跳事件

```csharp
client.Heartbeat += (sender, e) =>
{
    switch (e.Type)
    {
        case HeartbeatType.Request:
            Console.WriteLine("发送心跳请求");
            break;
        case HeartbeatType.Response:
            Console.WriteLine($"收到心跳响应，延迟: {e.ResponseTime}ms");
            break;
        case HeartbeatType.Timeout:
            Console.WriteLine("心跳超时");
            break;
    }
};
```

### 错误事件

```csharp
client.Error += (sender, e) =>
{
    Console.WriteLine($"发生错误: {e.Message}");
    Console.WriteLine($"错误类型: {e.ErrorType}");
    if (e.Exception != null)
    {
        Console.WriteLine($"异常详情: {e.Exception}");
    }
};
```

## 高级用法

### 发送JSON对象

```csharp
using Liam.TcpClient.Extensions;

var data = new { Name = "张三", Age = 30 };
await client.SendJsonAsync(data);

var response = await client.ReceiveJsonAsync<ResponseModel>();
```

### 文件传输

```csharp
// 发送文件
await client.SendFileAsync("path/to/file.txt");

// 接收文件
await client.ReceiveFileAsync("path/to/received-file.txt", fileSize);
```

### 批量发送

```csharp
var messages = new[]
{
    TcpMessage.CreateTextMessage("消息1"),
    TcpMessage.CreateTextMessage("消息2"),
    TcpMessage.CreateTextMessage("消息3")
};

var successCount = await client.SendBatchAsync(messages);
Console.WriteLine($"成功发送 {successCount} 条消息");
```

### 请求-响应模式

```csharp
var request = TcpMessage.CreateTextMessage("GET_STATUS");
var response = await client.SendRequestAsync(
    request,
    msg => msg.MessageType == TcpClientConstants.MessageTypes.Data,
    TimeSpan.FromSeconds(10)
);
```

### 等待特定消息

```csharp
var message = await client.WaitForMessageAsync(
    msg => msg.GetText().StartsWith("NOTIFICATION"),
    TimeSpan.FromMinutes(1)
);
```

## 连接池

### 配置连接池

```csharp
builder.Services.AddTcpClientPool(config, poolSize: 20);

var pool = serviceProvider.GetRequiredService<ITcpClientPool>();

// 获取客户端
var client = await pool.GetClientAsync();
try
{
    // 使用客户端
    await client.SendTextAsync("Hello");
}
finally
{
    // 归还客户端
    await pool.ReturnClientAsync(client);
}
```

### 池统计信息

```csharp
var stats = pool.GetStatistics();
Console.WriteLine($"总客户端: {stats.TotalClients}");
Console.WriteLine($"可用客户端: {stats.AvailableClients}");
Console.WriteLine($"忙碌客户端: {stats.BusyClients}");
Console.WriteLine($"使用率: {stats.UtilizationRate:F1}%");
```

## 性能监控

### 获取统计信息

```csharp
var statistics = client.GetStatistics();
Console.WriteLine($"连接成功率: {statistics.ConnectionSuccessRate:F1}%");
Console.WriteLine($"平均发送速度: {statistics.AverageSendRate:F2} bytes/s");
Console.WriteLine($"平均接收速度: {statistics.AverageReceiveRate:F2} bytes/s");
Console.WriteLine($"错误率: {statistics.ErrorRate:F2}%");
```

### 连接质量评估

```csharp
var quality = client.GetConnectionQuality();
Console.WriteLine($"连接质量评分: {quality:F1}/100");

var isHealthy = await client.CheckHealthAsync();
Console.WriteLine($"连接健康状态: {(isHealthy ? "健康" : "异常")}");
```

### 性能指标

```csharp
var metrics = client.GetPerformanceMetrics();
Console.WriteLine($"网络延迟: {metrics.NetworkLatency:F2}ms");
Console.WriteLine($"吞吐量: {metrics.Throughput:F2} bytes/s");
Console.WriteLine($"连接质量: {metrics.ConnectionQuality:F1}%");
```

### 诊断报告

```csharp
var report = await client.CreateDiagnosticReportAsync();
Console.WriteLine(report);
```

## 最佳实践

### 1. 资源管理
```csharp
// 使用using语句确保资源正确释放
using var client = CreateTcpClient(config);
await client.ConnectAsync();
// 客户端会在using块结束时自动释放
```

### 2. 异常处理
```csharp
try
{
    await client.ConnectAsync();
}
catch (ConnectionTimeoutException ex)
{
    Console.WriteLine($"连接超时: {ex.Timeout}");
}
catch (ConnectionException ex)
{
    Console.WriteLine($"连接失败: {ex.Message}");
}
```

### 3. 取消令牌
```csharp
using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
await client.ConnectAsync(cts.Token);
```

### 4. 配置验证
```csharp
var validation = config.Validate();
if (!validation.IsValid)
{
    foreach (var error in validation.Errors)
    {
        Console.WriteLine($"配置错误: {error}");
    }
}
```

## 与Liam.TcpServer集成

Liam.TcpClient与Liam.TcpServer完全兼容，使用相同的消息协议和通信格式：

```csharp
// 客户端代码
var message = TcpMessage.CreateTextMessage("Hello Server");
await client.SendMessageAsync(message);

// 服务器端会收到相同格式的消息
// 服务器响应也使用相同的消息格式
var response = await client.ReceiveMessageAsync();
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 增加连接超时时间
   - 确认服务器地址和端口正确

2. **SSL握手失败**
   - 检查证书配置
   - 验证服务器名称
   - 确认SSL协议版本

3. **心跳超时**
   - 调整心跳间隔和超时时间
   - 检查网络稳定性
   - 确认服务器支持心跳

4. **自动重连失败**
   - 检查重连配置
   - 确认网络恢复
   - 查看错误日志

### 调试技巧

```csharp
// 启用详细日志
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.SetMinimumLevel(LogLevel.Debug);
});

// 监听所有事件
client.ConnectionStateChanged += (s, e) => Console.WriteLine($"状态: {e.NewState}");
client.Error += (s, e) => Console.WriteLine($"错误: {e.Message}");

// 生成诊断报告
var report = await client.CreateDiagnosticReportAsync();
File.WriteAllText("diagnostic-report.txt", report);
```

## 版本历史

| 版本 | 发布日期 | 主要变更 |
|------|----------|----------|
| 1.0.1 | 2025-06-15 | **P0级安全修复**：移除Task.Run反模式，优化SSL/TLS协议配置安全性，添加ConfigureAwait(false)；**P1级代码质量**：修复编译器警告，完善IAsyncDisposable实现，优化可空引用类型标注；**性能改进**：真正的异步I/O操作，减少线程切换开销 |
| 1.0.0 | 2025-06-15 | 初始版本，支持基本TCP客户端功能、SSL/TLS、心跳检测、自动重连、连接池等 |

## 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](../../LICENSE) 文件。

## 贡献

欢迎提交问题和功能请求！请查看我们的[贡献指南](../../README.md#贡献指南)了解更多信息。

## 相关项目

- [Liam.TcpServer](../Liam.TcpServer/README.md) - TCP服务器通信库
- [Liam.Logging](../Liam.Logging/README.md) - 日志记录库
- [Liam.Cryptography](../Liam.Cryptography/README.md) - 加密功能库
